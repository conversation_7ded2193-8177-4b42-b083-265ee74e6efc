package net.dev.mod.mixin;

import net.dev.mod.api.DyeColorAPI;
import net.dev.mod.api.color.CustomDyeColor;
import net.minecraft.block.MapColor;
import net.minecraft.util.DyeColor;
import org.spongepowered.asm.mixin.*;
import org.spongepowered.asm.mixin.gen.Invoker;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Mixin(DyeColor.class)
public class DyeColorMixin {

    @Final @Mutable
    private static DyeColor[] field_8064; // $VALUES array

    @Final
    private int color;

    @Final
    private MapColor mapColor;

    @Final
    private int fireworkColor;

    @Final
    private int signColor;

    @Final
    private String name;

    /**
     * Invoker to access the private DyeColor constructor
     */
    @Invoker("<init>")
    private static DyeColor createDyeColor(String internalName, int ordinal, int id, String name,
                                          int color, MapColor mapColor, int fireworkColor, int signColor) {
        throw new AssertionError();
    }

    /**
     * Inject into the static initializer to add custom dye colors
     */
    @Inject(method = "<clinit>", at = @At("TAIL"))
    private static void addCustomDyeColors(CallbackInfo ci) {
        List<DyeColor> dyeColors;
        if (field_8064 != null) {
            dyeColors = new ArrayList<>(Arrays.asList(field_8064));
        } else {
            dyeColors = new ArrayList<>();
        }

        // Add all registered custom dye colors
        for (CustomDyeColor customColor : DyeColorAPI.getAllCustomDyeColors()) {
            int ordinal = dyeColors.size();
            DyeColor newDyeColor = createDyeColor(
                customColor.getName().toUpperCase(),
                ordinal,
                ordinal, // Using ordinal as ID for custom colors
                customColor.getName(),
                customColor.getColor(),
                customColor.getMapColor(),
                customColor.getFireworkColor(),
                customColor.getSignColor()
            );

            dyeColors.add(newDyeColor);
        }

        // Update the $VALUES array
        field_8064 = dyeColors.toArray(new DyeColor[0]);
    }
}
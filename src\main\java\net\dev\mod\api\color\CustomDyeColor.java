package net.dev.mod.api.color;

import net.minecraft.block.MapColor;
import net.minecraft.util.Identifier;

/**
 * Represents a custom dye color that can be registered through the DyeColorAPI.
 * This class holds all the information needed to create a new dye color.
 */
public class CustomDyeColor {
    private final Identifier id;
    private final String name;
    private final int color;
    private final MapColor mapColor;
    private final int fireworkColor;
    private final int signColor;
    private final int ordinal;
    
    public CustomDyeColor(Identifier id, String name, int color, MapColor mapColor, 
                         int fireworkColor, int signColor) {
        this.id = id;
        this.name = name;
        this.color = color;
        this.mapColor = mapColor;
        this.fireworkColor = fireworkColor;
        this.signColor = signColor;
        this.ordinal = -1; // Will be set by the mixin when the DyeColor is created
    }
    
    /**
     * Gets the unique identifier for this dye color.
     * 
     * @return The identifier
     */
    public Identifier getId() {
        return id;
    }
    
    /**
     * Gets the name of this dye color.
     * This is used for translation keys and internal references.
     * 
     * @return The name
     */
    public String getName() {
        return name;
    }
    
    /**
     * Gets the RGB color value.
     * 
     * @return The color in 0xRRGGBB format
     */
    public int getColor() {
        return color;
    }
    
    /**
     * Gets the red component of the color (0-255).
     * 
     * @return The red component
     */
    public int getRed() {
        return (color >> 16) & 0xFF;
    }
    
    /**
     * Gets the green component of the color (0-255).
     * 
     * @return The green component
     */
    public int getGreen() {
        return (color >> 8) & 0xFF;
    }
    
    /**
     * Gets the blue component of the color (0-255).
     * 
     * @return The blue component
     */
    public int getBlue() {
        return color & 0xFF;
    }
    
    /**
     * Gets the color components as a float array (0.0-1.0).
     * 
     * @return Array containing [red, green, blue] as floats
     */
    public float[] getColorComponents() {
        return new float[] {
            getRed() / 255.0f,
            getGreen() / 255.0f,
            getBlue() / 255.0f
        };
    }
    
    /**
     * Gets the map color used for this dye.
     * 
     * @return The map color
     */
    public MapColor getMapColor() {
        return mapColor;
    }
    
    /**
     * Gets the firework color for this dye.
     * 
     * @return The firework color in 0xRRGGBB format
     */
    public int getFireworkColor() {
        return fireworkColor;
    }
    
    /**
     * Gets the sign text color for this dye.
     * 
     * @return The sign color in 0xRRGGBB format
     */
    public int getSignColor() {
        return signColor;
    }
    
    /**
     * Gets the ordinal value for this dye color.
     * This is set when the DyeColor enum value is created.
     * 
     * @return The ordinal value
     */
    public int getOrdinal() {
        return ordinal;
    }
    
    /**
     * Gets the translation key for this dye color.
     * 
     * @return The translation key in the format "color.{namespace}.{name}"
     */
    public String getTranslationKey() {
        return "color." + id.getNamespace() + "." + name;
    }
    
    @Override
    public String toString() {
        return "CustomDyeColor{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", color=0x" + Integer.toHexString(color).toUpperCase() +
                ", ordinal=" + ordinal +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        CustomDyeColor that = (CustomDyeColor) obj;
        return id.equals(that.id);
    }
    
    @Override
    public int hashCode() {
        return id.hashCode();
    }
}

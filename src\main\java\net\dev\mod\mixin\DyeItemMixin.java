package net.dev.mod.mixin;

import net.minecraft.item.DyeItem;
import net.minecraft.item.Item;
import net.minecraft.util.DyeColor;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.gen.Invoker;

/**
 * Mixin to provide access to the DyeItem constructor for custom dye colors.
 */
@Mixin(DyeItem.class)
public interface DyeItemMixin {
    
    /**
     * Invoker to access the DyeItem constructor.
     * This allows us to create DyeItem instances with custom DyeColor values.
     * 
     * @param dyeColor The dye color for this item
     * @param settings The item settings
     * @return A new DyeItem instance
     */
    @Invoker("<init>")
    static DyeItem createDyeItem(DyeColor dyeColor, Item.Settings settings) {
        return new DyeItem(dyeColor, settings);
    }
}

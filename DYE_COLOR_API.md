# Dye Color API Documentation

This API allows you to add custom dye colors to Minecraft that integrate seamlessly with the vanilla dye system.

## Features

- **Custom Dye Colors**: Register new dye colors with custom RGB values
- **Map Color Integration**: Specify map colors for your dyes
- **Firework Support**: Custom colors for fireworks
- **Sign Text Colors**: Custom colors for sign text
- **Automatic Item Registration**: Create dye items for your colors
- **Color Utilities**: Helper methods for color manipulation
- **Translation Support**: Automatic translation key generation

## Quick Start

### 1. Register a Custom Dye Color

```java
import net.dev.mod.api.DyeColorAPI;
import net.dev.mod.api.color.ColorUtils;
import net.minecraft.block.MapColor;
import net.minecraft.util.Identifier;

// Register a rose gold dye color
CustomDyeColor roseGold = DyeColorAPI.registerDyeColor(
    new Identifier("mymod", "rose_gold"),
    "rose_gold",
    ColorUtils.rgb(233, 150, 122), // RGB color
    MapColor.PINK // Map color for this dye
);
```

### 2. Register a Dye Item

```java
import net.dev.mod.api.item.DyeItemRegistry;

// Register a dye item for the custom color
DyeItem roseGoldDye = DyeItemRegistry.registerDyeItem(
    roseGold, 
    DyeItemRegistry.createDyeItemSettings()
);
```

### 3. Add Translations

Add to your `assets/modid/lang/en_us.json`:

```json
{
    "color.mymod.rose_gold": "Rose Gold",
    "item.mymod.rose_gold_dye": "Rose Gold Dye"
}
```

## API Reference

### DyeColorAPI

Main class for registering and managing custom dye colors.

#### Methods

- `registerDyeColor(Identifier id, String name, int color, MapColor mapColor)` - Register a basic dye color
- `registerDyeColor(Identifier id, String name, int color, MapColor mapColor, int fireworkColor, int signColor)` - Register with custom firework/sign colors
- `getCustomDyeColor(Identifier id)` - Get a custom dye color by ID
- `getCustomDyeColor(String name)` - Get a custom dye color by name
- `getAllCustomDyeColors()` - Get all registered custom dye colors

### CustomDyeColor

Represents a custom dye color with all its properties.

#### Methods

- `getId()` - Get the unique identifier
- `getName()` - Get the name
- `getColor()` - Get the RGB color value
- `getRed()`, `getGreen()`, `getBlue()` - Get individual color components
- `getColorComponents()` - Get color as float array
- `getMapColor()` - Get the map color
- `getFireworkColor()` - Get the firework color
- `getSignColor()` - Get the sign text color
- `getTranslationKey()` - Get the translation key

### ColorUtils

Utility class for color operations.

#### Methods

- `rgb(int red, int green, int blue)` - Create RGB color from components
- `rgb(float red, float green, float blue)` - Create RGB color from float components
- `getRed(int color)`, `getGreen(int color)`, `getBlue(int color)` - Extract color components
- `rgbToHsv(int color)` - Convert RGB to HSV
- `hsvToRgb(float hue, float saturation, float value)` - Convert HSV to RGB
- `lighten(int color, float factor)` - Lighten a color
- `darken(int color, float factor)` - Darken a color
- `findClosestMapColor(int color)` - Find the closest vanilla map color

### DyeItemRegistry

Registry for managing dye items.

#### Methods

- `registerDyeItem(CustomDyeColor customColor, Item.Settings itemSettings)` - Register a dye item
- `getDyeItem(CustomDyeColor customColor)` - Get dye item for a custom color
- `getDyeItem(Identifier id)` - Get dye item by ID
- `createDyeItemSettings()` - Create standard dye item settings

## Advanced Usage

### Creating Color Variations

```java
// Create lighter and darker versions
CustomDyeColor lightBlue = DyeColorAPI.registerDyeColor(
    new Identifier("mymod", "light_blue"),
    "light_blue",
    ColorUtils.lighten(0x0000FF, 0.5f), // 50% lighter blue
    MapColor.LIGHT_BLUE
);

CustomDyeColor darkRed = DyeColorAPI.registerDyeColor(
    new Identifier("mymod", "dark_red"),
    "dark_red",
    ColorUtils.darken(0xFF0000, 0.3f), // 30% darker red
    MapColor.RED
);
```

### Using HSV Colors

```java
// Create a color using HSV (Hue, Saturation, Value)
int purpleColor = ColorUtils.hsvToRgb(270, 0.8f, 0.9f); // Purple with 80% saturation, 90% brightness

CustomDyeColor vibrantPurple = DyeColorAPI.registerDyeColor(
    new Identifier("mymod", "vibrant_purple"),
    "vibrant_purple",
    purpleColor,
    MapColor.PURPLE
);
```

### Custom Firework and Sign Colors

```java
CustomDyeColor specialDye = DyeColorAPI.registerDyeColor(
    new Identifier("mymod", "special"),
    "special",
    ColorUtils.rgb(255, 100, 50),  // Main color (orange-red)
    MapColor.ORANGE,
    ColorUtils.rgb(255, 150, 100), // Firework color (lighter)
    ColorUtils.rgb(200, 50, 25)    // Sign color (darker)
);
```

## Integration with Vanilla Systems

The custom dye colors automatically integrate with:

- **Wool dyeing** - Can be used to dye wool blocks
- **Leather armor dyeing** - Can dye leather armor pieces
- **Fireworks** - Custom firework colors
- **Signs** - Custom text colors for signs
- **Banners** - Can be used in banner patterns
- **Beds** - Can dye bed blocks
- **Glass** - Can create stained glass
- **Concrete** - Can create colored concrete

## Best Practices

1. **Use descriptive names** - Choose clear, descriptive names for your dye colors
2. **Choose appropriate map colors** - Select map colors that closely match your dye color
3. **Test color combinations** - Ensure your colors work well with existing blocks
4. **Provide translations** - Always include translation files for your colors
5. **Use ColorUtils** - Leverage the utility methods for consistent color operations

## Example Implementation

See `ModDyeColors.java` for a complete example implementation with 8 custom dye colors including Rose Gold, Turquoise, Lavender, Mint, Coral, Indigo, Crimson, and Forest Green.

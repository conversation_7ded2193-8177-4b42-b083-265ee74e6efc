package net.dev.mod.api;

import net.minecraft.block.MapColor;
import net.minecraft.util.DyeColor;
import net.minecraft.util.Identifier;
import net.dev.mod.api.color.CustomDyeColor;
import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;

/**
 * API for registering and managing custom dye colors.
 * This allows mods to add new dye colors that integrate with the vanilla dye system.
 */
public class DyeColorAPI {
    private static final Map<Identifier, CustomDyeColor> CUSTOM_DYE_COLORS = new HashMap<>();
    private static final Map<String, CustomDyeColor> CUSTOM_DYE_COLORS_BY_NAME = new HashMap<>();
    private static final List<CustomDyeColor> REGISTERED_COLORS = new ArrayList<>();
    
    /**
     * Registers a new custom dye color.
     * 
     * @param id The unique identifier for this dye color
     * @param name The name of the dye color (used for translation keys)
     * @param color The RGB color value (0xRRGGBB format)
     * @param mapColor The map color to use for this dye
     * @param fireworkColor The color to use in fireworks
     * @param signColor The color to use for sign text
     * @return The created CustomDyeColor instance
     */
    public static CustomDyeColor registerDyeColor(Identifier id, String name, int color, 
                                                  MapColor mapColor, int fireworkColor, int signColor) {
        if (CUSTOM_DYE_COLORS.containsKey(id)) {
            throw new IllegalArgumentException("Dye color with ID " + id + " is already registered!");
        }
        
        CustomDyeColor dyeColor = new CustomDyeColor(id, name, color, mapColor, fireworkColor, signColor);
        CUSTOM_DYE_COLORS.put(id, dyeColor);
        CUSTOM_DYE_COLORS_BY_NAME.put(name.toLowerCase(), dyeColor);
        REGISTERED_COLORS.add(dyeColor);
        
        return dyeColor;
    }
    
    /**
     * Registers a new custom dye color with simplified parameters.
     * The firework and sign colors will default to the main color.
     * 
     * @param id The unique identifier for this dye color
     * @param name The name of the dye color
     * @param color The RGB color value (0xRRGGBB format)
     * @param mapColor The map color to use for this dye
     * @return The created CustomDyeColor instance
     */
    public static CustomDyeColor registerDyeColor(Identifier id, String name, int color, MapColor mapColor) {
        return registerDyeColor(id, name, color, mapColor, color, color);
    }
    
    /**
     * Gets a custom dye color by its identifier.
     * 
     * @param id The identifier of the dye color
     * @return The CustomDyeColor instance, or null if not found
     */
    public static CustomDyeColor getCustomDyeColor(Identifier id) {
        return CUSTOM_DYE_COLORS.get(id);
    }
    
    /**
     * Gets a custom dye color by its name.
     * 
     * @param name The name of the dye color (case-insensitive)
     * @return The CustomDyeColor instance, or null if not found
     */
    public static CustomDyeColor getCustomDyeColor(String name) {
        return CUSTOM_DYE_COLORS_BY_NAME.get(name.toLowerCase());
    }
    
    /**
     * Gets all registered custom dye colors.
     * 
     * @return A list of all registered CustomDyeColor instances
     */
    public static List<CustomDyeColor> getAllCustomDyeColors() {
        return new ArrayList<>(REGISTERED_COLORS);
    }
    
    /**
     * Checks if a dye color with the given identifier is registered.
     * 
     * @param id The identifier to check
     * @return true if the dye color is registered, false otherwise
     */
    public static boolean isRegistered(Identifier id) {
        return CUSTOM_DYE_COLORS.containsKey(id);
    }
    
    /**
     * Gets the total number of registered custom dye colors.
     * 
     * @return The number of registered custom dye colors
     */
    public static int getCustomDyeColorCount() {
        return REGISTERED_COLORS.size();
    }
    
    /**
     * Internal method to get the next available ordinal for a new dye color.
     * This is used by the mixin to assign ordinals to custom dye colors.
     * 
     * @return The next available ordinal
     */
    public static int getNextOrdinal() {
        return DyeColor.values().length + REGISTERED_COLORS.size();
    }
}

package net.dev.mod.api.item;

import net.dev.mod.ServerMod;
import net.dev.mod.api.color.CustomDyeColor;
import net.dev.mod.mixin.DyeItemMixin;
import net.minecraft.item.DyeItem;
import net.minecraft.item.Item;
import net.minecraft.registry.Registries;
import net.minecraft.registry.Registry;
import net.minecraft.registry.RegistryKey;
import net.minecraft.registry.RegistryKeys;
import net.minecraft.util.DyeColor;
import net.minecraft.util.Identifier;

import java.util.HashMap;
import java.util.Map;

/**
 * Registry for managing dye items associated with custom dye colors.
 */
public class DyeItemRegistry {
    private static final Map<CustomDyeColor, DyeItem> CUSTOM_DYE_ITEMS = new HashMap<>();
    private static final Map<Identifier, DyeItem> DYE_ITEMS_BY_ID = new HashMap<>();
    
    /**
     * Registers a dye item for a custom dye color.
     * 
     * @param customColor The custom dye color
     * @param itemSettings The item settings to use
     * @return The created DyeItem
     */
    public static DyeItem registerDyeItem(CustomDyeColor customColor, Item.Settings itemSettings) {
        Identifier itemId = Identifier.of(customColor.getId().getNamespace(),
                                         customColor.getId().getPath() + "_dye");
        
        // Create the dye item
        // Note: We'll need to use reflection or a mixin to create DyeItem with custom DyeColor
        DyeItem dyeItem = new DyeItem(customColor, itemSettings);
        
        // Register the item
        RegistryKey<Item> itemKey = RegistryKey.of(RegistryKeys.ITEM, itemId);
        Registry.register(Registries.ITEM, itemKey, dyeItem);
        
        // Store in our maps
        CUSTOM_DYE_ITEMS.put(customColor, dyeItem);
        DYE_ITEMS_BY_ID.put(itemId, dyeItem);
        
        return dyeItem;
    }
    
    /**
     * Gets the dye item for a custom dye color.
     * 
     * @param customColor The custom dye color
     * @return The DyeItem, or null if not registered
     */
    public static DyeItem getDyeItem(CustomDyeColor customColor) {
        return CUSTOM_DYE_ITEMS.get(customColor);
    }
    
    /**
     * Gets a dye item by its identifier.
     * 
     * @param id The item identifier
     * @return The DyeItem, or null if not found
     */
    public static DyeItem getDyeItem(Identifier id) {
        return DYE_ITEMS_BY_ID.get(id);
    }
    
    /**
     * Gets all registered custom dye items.
     * 
     * @return A map of custom dye colors to their items
     */
    public static Map<CustomDyeColor, DyeItem> getAllCustomDyeItems() {
        return new HashMap<>(CUSTOM_DYE_ITEMS);
    }
    
    /**
     * Checks if a dye item is registered for the given custom color.
     * 
     * @param customColor The custom dye color
     * @return true if a dye item is registered, false otherwise
     */
    public static boolean hasDyeItem(CustomDyeColor customColor) {
        return CUSTOM_DYE_ITEMS.containsKey(customColor);
    }
    
    /**
     * Creates a custom dye item using the DyeItemMixin.
     *
     * @param customColor The custom dye color
     * @param settings The item settings
     * @return The created DyeItem
     */
    private static DyeItem createCustomDyeItem(CustomDyeColor customColor, Item.Settings settings) {
        // For now, we'll create a basic DyeItem with a vanilla color as a fallback
        // TODO: Once the DyeColor mixin is working, we can use the custom DyeColor
        return DyeItemMixin.createDyeItem(DyeColor.WHITE, settings.registryKey(RegistryKey.of(RegistryKeys.ITEM,
         Identifier.of(ServerMod.MOD_ID, customColor.getId().getPath() + "_dye"))));
    }
    
    /**
     * Helper method to create standard dye item settings.
     * 
     * @return Item.Settings configured for dye items
     */
    public static Item.Settings createDyeItemSettings() {
        return new Item.Settings();
    }
}

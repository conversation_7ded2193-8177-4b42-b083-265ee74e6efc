package net.dev.mod.api.item;

import net.dev.mod.ServerMod;
import net.dev.mod.api.color.CustomDyeColor;
import net.dev.mod.mixin.DyeItemMixin;
import net.minecraft.item.DyeItem;
import net.minecraft.item.Item;
import net.minecraft.registry.Registries;
import net.minecraft.registry.Registry;
import net.minecraft.registry.RegistryKey;
import net.minecraft.registry.RegistryKeys;
import net.minecraft.util.DyeColor;
import net.minecraft.util.Identifier;

import java.util.HashMap;
import java.util.Map;

/**
 * Registry for managing dye items associated with custom dye colors.
 * This registry integrates with the vanilla DyeColor system through mixins.
 */
public class DyeItemRegistry {
    private static final Map<CustomDyeColor, DyeItem> CUSTOM_DYE_ITEMS = new HashMap<>();
    private static final Map<Identifier, DyeItem> DYE_ITEMS_BY_ID = new HashMap<>();
    private static final Map<DyeColor, DyeItem> DYE_ITEMS_BY_COLOR = new HashMap<>();
    
    /**
     * Registers a dye item for a custom dye color.
     * This method integrates with the vanilla DyeColor system by finding the corresponding
     * DyeColor that was created by the DyeColorMixin.
     *
     * @param customColor The custom dye color
     * @param itemSettings The item settings to use
     * @return The created DyeItem
     */
    public static DyeItem registerDyeItem(CustomDyeColor customColor, Item.Settings itemSettings) {
        Identifier itemId = Identifier.of(ServerMod.MOD_ID, customColor.getName() + "_dye");

        // Find the corresponding DyeColor that was created by the mixin
        DyeColor dyeColor = findDyeColorForCustomColor(customColor);
        if (dyeColor == null) {
            throw new IllegalStateException("No DyeColor found for custom color: " + customColor.getName() +
                ". Make sure the DyeColorMixin is working properly.");
        }

        // Create the dye item using the mixin
        DyeItem dyeItem = DyeItemMixin.createDyeItem(dyeColor, itemSettings);

        // Register the item in the game registry
        RegistryKey<Item> itemKey = RegistryKey.of(RegistryKeys.ITEM, itemId);
        Registry.register(Registries.ITEM, itemKey, dyeItem);

        // Store in our maps for easy lookup
        CUSTOM_DYE_ITEMS.put(customColor, dyeItem);
        DYE_ITEMS_BY_ID.put(itemId, dyeItem);
        DYE_ITEMS_BY_COLOR.put(dyeColor, dyeItem);

        return dyeItem;
    }

    /**
     * Finds the DyeColor that corresponds to a CustomDyeColor.
     * This searches through all DyeColor values to find one with matching properties.
     *
     * @param customColor The custom dye color to find
     * @return The corresponding DyeColor, or null if not found
     */
    private static DyeColor findDyeColorForCustomColor(CustomDyeColor customColor) {
        for (DyeColor dyeColor : DyeColor.values()) {
            // Check if this DyeColor has the same name as our custom color
            // Use toString() or name() to get the enum name
            if (dyeColor.name().toLowerCase().equals(customColor.getName().toLowerCase())) {
                return dyeColor;
            }
        }
        return null;
    }
    
    /**
     * Gets the dye item for a custom dye color.
     * 
     * @param customColor The custom dye color
     * @return The DyeItem, or null if not registered
     */
    public static DyeItem getDyeItem(CustomDyeColor customColor) {
        return CUSTOM_DYE_ITEMS.get(customColor);
    }
    
    /**
     * Gets a dye item by its identifier.
     * 
     * @param id The item identifier
     * @return The DyeItem, or null if not found
     */
    public static DyeItem getDyeItem(Identifier id) {
        return DYE_ITEMS_BY_ID.get(id);
    }
    
    /**
     * Gets all registered custom dye items.
     *
     * @return A map of custom dye colors to their items
     */
    public static Map<CustomDyeColor, DyeItem> getAllCustomDyeItems() {
        return new HashMap<>(CUSTOM_DYE_ITEMS);
    }

    /**
     * Gets a dye item by its DyeColor.
     * This allows integration with vanilla systems that work with DyeColor.
     *
     * @param dyeColor The DyeColor
     * @return The DyeItem, or null if not found
     */
    public static DyeItem getDyeItem(DyeColor dyeColor) {
        return DYE_ITEMS_BY_COLOR.get(dyeColor);
    }

    /**
     * Checks if a dye item is registered for the given custom color.
     *
     * @param customColor The custom dye color
     * @return true if a dye item is registered, false otherwise
     */
    public static boolean hasDyeItem(CustomDyeColor customColor) {
        return CUSTOM_DYE_ITEMS.containsKey(customColor);
    }

    /**
     * Checks if a dye item is registered for the given DyeColor.
     *
     * @param dyeColor The DyeColor
     * @return true if a dye item is registered, false otherwise
     */
    public static boolean hasDyeItem(DyeColor dyeColor) {
        return DYE_ITEMS_BY_COLOR.containsKey(dyeColor);
    }

    /**
     * Gets the total number of registered custom dye items.
     *
     * @return The number of registered custom dye items
     */
    public static int getCustomDyeItemCount() {
        return CUSTOM_DYE_ITEMS.size();
    }
    
    /**
     * Helper method to create standard dye item settings.
     *
     * @return Item.Settings configured for dye items
     */
    public static Item.Settings createDyeItemSettings() {
        return new Item.Settings();
    }

    /**
     * Gets the CustomDyeColor associated with a DyeColor.
     * This allows reverse lookup from vanilla DyeColor to our custom color data.
     *
     * @param dyeColor The DyeColor to look up
     * @return The associated CustomDyeColor, or null if not found
     */
    public static CustomDyeColor getCustomDyeColor(DyeColor dyeColor) {
        for (Map.Entry<CustomDyeColor, DyeItem> entry : CUSTOM_DYE_ITEMS.entrySet()) {
            DyeItem item = DYE_ITEMS_BY_COLOR.get(dyeColor);
            if (item != null && item.equals(entry.getValue())) {
                return entry.getKey();
            }
        }
        return null;
    }
}
